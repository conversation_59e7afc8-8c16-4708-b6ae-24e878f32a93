import React from 'react';
import { Facebook, Instagram, Twitter, Linkedin } from 'lucide-react';
import { MinimalistHero } from '@/components/ui/minimalist-hero';

const JookaHeroDemo = () => {
  const navLinks = [
    { label: 'HOME', href: '/' },
    { label: 'SHOP', href: '/shop' },
    { label: 'ABOUT', href: '/about' },
    { label: 'CART', href: '/cart' },
  ];

  const socialLinks = [
    { icon: Facebook, href: '#' },
    { icon: Instagram, href: '#' },
    { icon: Twitter, href: '#' },
    { icon: Linkedin, href: '#' },
  ];

  return (
    <div className="bg-black text-gold">
      <MinimalistHero
        logoText="JOOKA"
        navLinks={navLinks}
        mainText="Discover luxury fashion that defines elegance and sophistication. Each piece is carefully curated to embody timeless style and exceptional quality."
        readMoreLink="/about"
        imageSrc="/hero-img.png"
        imageAlt="Luxury fashion model showcasing JOOKA's elegant collection"
        overlayText={{
          part1: 'luxury',
          part2: 'redefined.',
        }}
        socialLinks={socialLinks}
        locationText="New York, NY"
        className="bg-black text-gold font-serif"
      />
    </div>
  );
};

export default JookaHeroDemo;
