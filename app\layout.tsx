import './globals.css'
import { Inter } from 'next/font/google'
import Header from '@/components/Header'
import Footer from '@/components/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'JOOKA – Natural Elegance',
  description: 'Luxury fashion designed with timeless sophistication',
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={inter.className + ' bg-black text-gold'}>
        <main className="min-h-screen">{children}</main>
        <Footer />
      </body>
    </html>
  )
}
